<route lang="json5">
{
  style: {
    navigationBarTitleText: '商品详情',
    navigationStyle: 'custom',
    navigationBarTextStyle: 'white',
  },
}
</route>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { FromPlatform, ServerType, GoodsStatus } from '@/enums'
import { OrderDirection } from '@/enums/httpEnum'
import {
  mallGoodsLoadApi,
  MallGoodsLoadRes,
  orderCreateApi,
  OrderCreateParams,
  payMallOrderApi,
  SkuListData,
  sumbitMallOrder,
  mallCommentRecordPageApi,
  MallCommentRecordPageRes,
} from '@/service/shoppingMallApi'
import MallSwiper from '@/components/mallSwiper.vue'
import PriceBox from '@/components/Price/PriceBox.vue'
import { addressPageApi } from '@/service/addressPageApi'
import { wxRequestPayment } from '@/components/mix'
import CsBtnLeftButton from '@/components/customer/csBtnLeftButton.vue'
import { msgModalStore } from '@/store/msgModalStore'
import { Color } from '@/enums/colorEnum'
import dayjs from 'dayjs'

// 获取屏幕边界到安全区域距离
const { safeAreaInsets, statusBarHeight, windowWidth, windowHeight } = uni.getSystemInfoSync()

const useMsgModalStore = msgModalStore()

const userStore = useUserStore()

const loading = ref(false)
const mallData = ref<MallGoodsLoadRes['data']>()
const carousesList = ref<string[]>([]) // 主图 + 轮播图
const selectArr = ref<string[]>([])
const isSheetShow = ref(false)
const isShowPreviewImg = ref(false)
const quantity = ref(1)
const previewImgUrl = ref<string>()
const allGoodsImg = ref<string[]>([])
const imgCurrent = ref(0)
const activeSkuItem = ref<SkuListData>()
let goodsId = 0

// 评价相关数据
const commentList = ref<MallCommentRecordPageRes['data']>([])
const commentTotal = ref(0)
const commentLoading = ref(false)
const commentPage = ref(1)
const commentPageSize = ref(10)
const hasMoreComment = ref(true)

// 评论弹窗相关
const isCommentSheetShow = ref(false)
const allCommentList = ref<MallCommentRecordPageRes['data']>([])
const allCommentLoading = ref(false)
const allCommentPage = ref(1)
const allCommentPageSize = ref(10)
const hasMoreAllComment = ref(true)

const addressData = reactive({
  addressDetail: '',
  district: '',
  phone: '',
  realName: '',
})

onLoad((option) => {
  goodsId = Number(option.goodsId)
  // 预防首页太快没登录，这里再自动判断，没登录再登录
  userStore.login().then(() => {
    mallGoodsLoadApi(goodsId).then((res) => {
      if (res.data.goodsStatus === GoodsStatus.listed) {
        mallData.value = res.data
        carousesList.value = [res.data.imageUrl, ...res.data.sliderImages.split('|')]
        // activeSkuItem.value = res.data.skuList[0]
        selectArr.value = res.data.skuList[0].specValueNameStr.split(',')
        // getPreviewImgAndPrice()
        getAllGoodsImg()
        // 加载评价数据
        getCommentList()
      } else {
        uni.showModal({
          title: '商品已下架',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              uni.navigateBack()
            } else if (res.cancel) {
              uni.navigateBack()
            }
          },
        })
      }
    })
  })
})

// 获取评价列表（默认只显示前2个）
const getCommentList = () => {
  if (commentLoading.value) return

  commentLoading.value = true
  mallCommentRecordPageApi({
    goodsId,
    pageIndex: 1,
    pageSize: 2, // 只获取前2个评论
    orderDirection: OrderDirection.desc,
    orderBy: 'commentTime',
    needTotalCount: true,
  })
    .then((res) => {
      commentList.value = res.data
      commentTotal.value = res.totalCount
    })
    .finally(() => {
      commentLoading.value = false
    })
}

// 显示全部评论弹窗
const showAllComments = () => {
  isCommentSheetShow.value = true
  // 如果还没有加载过全部评论，则加载第一页
  if (allCommentList.value.length === 0) {
    getAllCommentList()
  }
}

// 获取全部评论列表
const getAllCommentList = () => {
  if (allCommentLoading.value || !hasMoreAllComment.value) return

  allCommentLoading.value = true
  mallCommentRecordPageApi({
    goodsId,
    pageIndex: allCommentPage.value,
    pageSize: allCommentPageSize.value,
    orderDirection: OrderDirection.desc,
    orderBy: 'commentTime',
    needTotalCount: true,
  })
    .then((res) => {
      if (allCommentPage.value === 1) {
        allCommentList.value = res.data
      } else {
        allCommentList.value.push(...res.data)
      }
      hasMoreAllComment.value = allCommentPage.value * allCommentPageSize.value < res.totalCount
      allCommentPage.value++
    })
    .finally(() => {
      allCommentLoading.value = false
    })
}

// 关闭评论弹窗
const closeCommentSheet = () => {
  isCommentSheetShow.value = false
}

// 滚动到底部加载更多评论
const onCommentScrolltolower = () => {
  if (hasMoreAllComment.value && !allCommentLoading.value) {
    getAllCommentList()
  }
}

// 处理评价图片
const getCommentImages = (imagesStr: string) => {
  if (!imagesStr) return []
  const arr = imagesStr.split(',')
  // 最后一个元素如果为空，则去掉
  if (!arr[arr.length - 1]) {
    arr.pop()
  }
  return arr
}

// 预览评价图片
const previewCommentImage = (images: string[], index: number) => {
  uni.previewImage({
    urls: images,
    current: images[index],
  })
}

onShow(() => {
  getAddressData()
})

const getAddressData = () => {
  addressPageApi({
    userId: userStore.userId,
  }).then((res) => {
    if (res.data && res.data.length > 0) {
      const d = res.data.filter((item) => item.isDefault)
      if (d.length > 0) {
        addressData.addressDetail = d[0].addressDetail
        addressData.district = d[0].district
        addressData.phone = d[0].phone
        addressData.realName = d[0].realName
      } else {
        addressData.addressDetail = res.data[0].addressDetail
        addressData.district = res.data[0].district
        addressData.phone = res.data[0].phone
        addressData.realName = res.data[0].realName
      }
    }
  })
}

// TODO 测试库存为0情况
const handleSubmit = () => {
  if (loading.value || activeSkuItem.value?.stock === 0) {
    return
  }

  const okFunc = () => {
    loading.value = false
    uni.hideLoading()
  }

  if (addressData.phone) {
    loading.value = true
    uni.showLoading({ title: '提交订单中...' })
    const params: OrderCreateParams = {
      attrDetailValueId: activeSkuItem.value.attrDetailValueId,
      goodsId,
      number: quantity.value,
      serverType: ServerType.labelPrint,
      userId: useUserStore().userId,
    }
    params.fromTo = FromPlatform.wx
    orderCreateApi(params)
      .then((orderCodeRes) => {
        sumbitMallOrder({
          addressDetail: addressData.addressDetail,
          contact: addressData.realName,
          contactPhone: addressData.phone,
          district: addressData.district,
          // couponId: 0,
          // email: '',
          orderCode: orderCodeRes.data.orderCode,
        })
          .then((submitRes) => {
            payMallOrderApi({ orderCode: submitRes.data.orderCode })
              .then((payRes) => {
                const url = `/pages/paymentSuccess/index?orderCode=${submitRes.data.orderCode}`
                if (payRes.data.isNeedToPay) {
                  wxRequestPayment(payRes.data)
                    .then(() => {
                      okFunc()
                      // 跳转到支付成功页面，并传递订单编号和总价
                      uni.redirectTo({
                        url,
                      })
                    })
                    .catch(() => {
                      okFunc()
                    })
                } else {
                  uni.redirectTo({
                    url,
                  })
                }
              })
              .catch(() => {
                okFunc()
              })
          })
          .catch(() => {
            okFunc()
          })
      })
      .catch(() => {
        okFunc()
      })
  } else {
    useMsgModalStore
      .confirm({
        title: '温馨提示',
        content: '请先添加地址',
      })
      .then(() => {
        handleAddressClick()
      })
  }
}

const isSelectTag = (index: number, specValue: string) => {
  if (selectArr.value.length === 0) {
    return false
  }
  return selectArr.value[index] === specValue
}

const handleSelectTag = (index: number, specValue: string, length: number) => {
  if (selectArr.value.length === 0) {
    for (let i = 0; i < length; i++) {
      selectArr.value.push('')
    }
  }
  selectArr.value[index] = specValue
  imgCurrent.value = mallData.value.skuList?.findIndex(
    (item) => item.specValueNameStr === selectArr.value.join(','),
  )
  activeSkuItem.value = mallData.value.skuList[imgCurrent.value]
  getPreviewImgAndPrice()
}

const getPreviewImgAndPrice = () => {
  previewImgUrl.value = activeSkuItem.value.goodsImage ?? mallData.value.imageUrl
  // price.value = activeSkuItem.value.originalPrice * quantity.value
}

const getAllGoodsImg = () => {
  mallData.value.skuList?.forEach((item) => {
    if (item.goodsImage) {
      allGoodsImg.value.push(item.goodsImage)
    } else {
      allGoodsImg.value.push(mallData.value.imageUrl)
    }
  })
}

const initSelectSkuItem = () => {
  activeSkuItem.value = mallData.value.skuList[0]
  getPreviewImgAndPrice()
}

const showSelectSheet = () => {
  if (!activeSkuItem.value) {
    initSelectSkuItem()
  }
  isSheetShow.value = true
}

const closeSheetShow = () => {
  isSheetShow.value = false
}

const toPreviewListImg = () => {
  imgCurrent.value = mallData.value.skuList?.findIndex(
    (item) => item.attrDetailValueId === activeSkuItem.value.attrDetailValueId,
  )
  isShowPreviewImg.value = true
}

const closePreviewShow = () => {
  isShowPreviewImg.value = false
}

const getSelectTagName = (arr: string[]) => {
  // 将数组转为字符串
  return arr.join(' - ')
}

const activeSkuName = computed(() => {
  return selectArr.value.join('，')
})

const onChangePreviewImg = (current: number) => {
  // 兼容两种类型的事件参数
  const index = typeof current === 'object' ? (current as any).current : current
  activeSkuItem.value = mallData.value.skuList[index]
  selectArr.value = mallData.value.skuList[index].specValueNameStr.split(',')
  // console.log('onChangePreviewImg', activeSkuItem.value, selectArr.value)
}

const handleAddressClick = () => {
  uni.navigateTo({
    url: '/pages/addressPage/index?toSelect=true',
  })
}

// TODO 支付成功无跳转
// TODO up-number-box ui错位
</script>
<template>
  <mall-swiper
    :carouses-list="carousesList"
    :safeAreaInsets="safeAreaInsets"
    :statusBarHeight="statusBarHeight"
    :windowWidth="windowWidth"
  />
  <view class="bg-white rounded-b">
    <view class="px-4 pb-4 pt-3 text-2xl font-bold">{{ mallData?.goodsName }}</view>
    <!--    <view :class="DESCRIPTION_STR[descriptionServiceType].colorClass" class="pl-2">
          <view class="sf-title-round pt-4 pl-3 pb-2">
            <view class="sf-title-text text-2xl font-bold">
              {{ DESCRIPTION_STR[descriptionServiceType].title }}
            </view>
            <view class="text-sm o-color-aid">
              {{ DESCRIPTION_STR[descriptionServiceType].typeStr }}
            </view>
            <view class="text-xs">{{ DESCRIPTION_STR[descriptionServiceType].description }}</view>
          </view>
        </view>-->
  </view>
  <view
    class="bg-white p-5 text-sm mt-2 overflow-hidden relative rounded"
    style="height: 14vw"
    @click="showSelectSheet"
  >
    <view>
      <view class="font-bold mt-2 text-base">
        {{ mallData?.specList[0]?.specName }}
      </view>
      <view class="flex flex-wrap gap-2">
        <view
          v-for="(subItem, subIndex) in mallData?.specList[0]?.specValue"
          :key="subIndex"
          class="f-tag mt-3 o-border-gray-tag"
        >
          {{ subItem }}
        </view>
      </view>
    </view>
    <view class="f-list-hidden w-full absolute bottom-0 left-0 z-1"></view>
    <view class="absolute flex gap-1 text-gray right-6 top-6">
      <view>选择</view>
      <up-icon name="arrow-right" :size="14"></up-icon>
    </view>
  </view>

  <!-- 商品评价部分 -->
  <view class="bg-white mt-2 p-4 rounded">
    <view class="flex justify-between items-center mb-4">
      <view class="text-lg font-bold">商品评价 ({{ commentTotal }})</view>
      <view class="comment-view-all" v-if="commentTotal > 2" @click="showAllComments">
        <view>查看全部</view>
        <up-icon name="arrow-right" :size="14"></up-icon>
      </view>
    </view>

    <view v-if="commentList.length === 0" class="py-4 text-center text-gray">暂无评价</view>

    <view v-else class="comment-list">
      <view
        v-for="(item, index) in commentList"
        :key="index"
        class="comment-item"
        :class="{ 'f-border-bottom': index !== commentList.length - 1 }"
      >
        <view class="comment-header">
          <up-avatar class="comment-avatar" :size="30" :src="item.avatarUrl"></up-avatar>
          <view class="comment-user-info">
            <view class="comment-username">{{ item.nickName || '匿名用户' }}</view>
            <view class="comment-time">
              {{ dayjs(item.commentTime).format('YYYY-MM-DD') }}
            </view>
          </view>
          <up-rate
            class="comment-rating"
            v-model="item.score"
            :activeColor="Color.primary"
            size="16"
            readonly
            gutter="2"
          ></up-rate>
        </view>

        <view class="comment-content">{{ item.commentContent }}</view>

        <view v-if="item.images" class="comment-images">
          <view
            v-for="(img, imgIndex) in getCommentImages(item.images)"
            :key="imgIndex"
            class="comment-image"
            @click="previewCommentImage(getCommentImages(item.images), imgIndex)"
          >
            <up-image :src="img" :width="80" :height="80" mode="aspectFill" class="rounded" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="bg-white mt-2 py-4 rounded overflow-hidden">
    <up-parse :content="mallData?.goodsContent"></up-parse>
  </view>
  <view class="p-10"></view>
  <view
    class="f-bottom-box h-auto box-border bg-white fixed w-full px-4 pb-3 pt-2 left-0 bottom-0 z-2 flex gap-2"
  >
    <cs-btn-left-button />
    <view
      class="p-3 flex-grow-1 flex items-center justify-center color-white font-bold rounded o-bg-primary"
      @click="showSelectSheet"
    >
      立即购买
    </view>
  </view>
  <up-action-sheet
    :show="isSheetShow"
    :closeOnClickOverlay="true"
    :round="10"
    @close="isSheetShow = false"
  >
    <view
      class="px-4 pt-4 pb-3 relative text-left flex flex-col"
      :style="{ height: windowHeight - safeAreaInsets?.top - 160 + 'px' }"
    >
      <up-icon
        class="absolute text-gray top-6 right-6"
        name="close"
        :size="14"
        @click="closeSheetShow"
      ></up-icon>
      <view class="mt-5">
        <view class="flex gap-4 justify-between items-end" @click="handleAddressClick">
          <template v-if="addressData.phone">
            <view>
              <view class="flex gap-4">
                <text>{{ addressData.realName }}</text>
                <text>{{ addressData.phone }}</text>
              </view>
              <view class="">{{ addressData.district }}{{ addressData.addressDetail }}</view>
            </view>
            <view class="shrink-0 o-color-aid pr-1">
              <up-icon name="arrow-right" :size="14"></up-icon>
            </view>
          </template>
          <view v-else class="color-red flex space-x-1">
            <up-icon name="edit-pen" :color="Color.primary" :size="28"></up-icon>
            <text>请添加收货地址</text>
          </view>
        </view>
      </view>
      <view class="o-line mt-2 mb-2"></view>
      <view class="flex px-4 mb-4 gap-3">
        <up-image
          :width="100"
          :height="100"
          :src="allGoodsImg[imgCurrent]"
          mode="aspectFill"
          @click="toPreviewListImg"
        />
        <view class="flex flex-col justify-between">
          <view>
            <price-box
              :price="parseFloat((activeSkuItem?.originalPrice * quantity).toFixed(2))"
              notShowDecimal
              :size="40"
              class="color-red"
            />
            <view v-if="activeSkuItem?.stock === 0" class="mt-2 text-sm text-gray">
              商品暂无库存
            </view>
          </view>
          <up-number-box class="mt-6" v-model="quantity"></up-number-box>
        </view>
      </view>
      <view class="mt-4 pb-2 flex-1 relative">
        <view class="text-sm" v-for="(item, index) in mallData?.specList" :key="item?.specId">
          <view class="font-bold mb-2 text-base">
            {{ item?.specName }}
          </view>
          <view class="flex flex-wrap gap-2 mb-3">
            <view
              v-for="(subItem, subIndex) in item?.specValue"
              :key="subIndex"
              class="f-tag flex gap-1"
              :class="isSelectTag(index, subItem) ? 'o-border-blue-tag' : 'o-border-gray-tag'"
              @click="handleSelectTag(index, subItem, mallData?.specList?.length)"
            >
              <text>{{ subItem }}</text>
            </view>
          </view>
        </view>
      </view>
      <view
        class="p-3 flex items-center justify-center color-white font-bold rounded gap-2"
        :class="loading || activeSkuItem?.stock === 0 ? 'o-bg-primary-disable' : 'o-bg-primary'"
        @click="handleSubmit"
      >
        <view v-if="activeSkuItem?.stock === 0">暂无库存</view>
        <template v-else>
          <view>立即支付</view>
          <price-box
            :price="parseFloat((activeSkuItem?.originalPrice * quantity).toFixed(2))"
            notShowDecimal
            :size="40"
            class="color-white"
          />
        </template>
      </view>
    </view>
  </up-action-sheet>
  <view
    class="f-img-preview absolute top-0 left-0 bg-black color-white flex flex-col justify-center items-center"
    style="z-index: 99999"
    v-if="isShowPreviewImg"
    @click="closePreviewShow"
  >
    <up-icon
      class="absolute color-white right-6"
      name="close"
      :size="16"
      :style="{ top: safeAreaInsets?.top + 60 + 'px' }"
    ></up-icon>
    <view class="w-full">
      <up-swiper
        v-model:current="imgCurrent"
        :height="windowWidth"
        :list="allGoodsImg"
        :autoplay="false"
        indicatorStyle="top:10px;right: 20px"
        @change="onChangePreviewImg"
      ></up-swiper>
      <view class="mt-4 p-4 text-center">{{ activeSkuName }}</view>
    </view>
  </view>

  <!-- 全部评论弹窗 -->
  <up-action-sheet
    :show="isCommentSheetShow"
    :closeOnClickOverlay="true"
    :round="10"
    @close="closeCommentSheet"
  >
    <view
      class="px-4 pt-4 pb-3 relative"
      :style="{ height: windowHeight - safeAreaInsets?.top - 160 + 'px' }"
    >
      <view class="flex justify-between items-center mb-4">
        <view class="text-lg font-bold">全部评价 ({{ commentTotal }})</view>
        <up-icon class="text-gray" name="close" :size="20" @click="closeCommentSheet"></up-icon>
      </view>

      <scroll-view
        class="comment-scroll-view"
        scroll-y
        :style="{ height: windowHeight - safeAreaInsets?.top - 180 + 'px' }"
        @scrolltolower="onCommentScrolltolower"
      >
        <view
          v-if="allCommentList.length === 0 && !allCommentLoading"
          class="py-8 text-center text-gray"
        >
          暂无评价
        </view>

        <view v-else class="comment-list text-left">
          <view
            v-for="(item, index) in allCommentList"
            :key="index"
            class="comment-item"
            :class="{ 'f-border-bottom': index !== allCommentList.length - 1 }"
          >
            <view class="comment-header">
              <up-avatar class="comment-avatar" :size="30" :src="item.avatarUrl"></up-avatar>
              <view class="comment-user-info">
                <view class="comment-username">{{ item.nickName || '匿名用户' }}</view>
                <view class="comment-time">
                  {{ dayjs(item.commentTime).format('YYYY-MM-DD') }}
                </view>
              </view>
              <up-rate
                class="comment-rating"
                v-model="item.score"
                :activeColor="Color.primary"
                size="16"
                readonly
                gutter="2"
              ></up-rate>
            </view>

            <view class="comment-content">{{ item.commentContent }}</view>

            <view v-if="item.images" class="comment-images">
              <view
                v-for="(img, imgIndex) in getCommentImages(item.images)"
                :key="imgIndex"
                class="comment-image"
                @click="previewCommentImage(getCommentImages(item.images), imgIndex)"
              >
                <up-image :src="img" :width="80" :height="80" mode="aspectFill" class="rounded" />
              </view>
            </view>
          </view>

          <!-- 加载更多提示 -->
          <view v-if="hasMoreAllComment || allCommentLoading" class="comment-loading">
            <view v-if="allCommentLoading">加载中...</view>
            <view v-else>上拉加载更多</view>
          </view>

          <view v-else-if="allCommentList.length > 0" class="comment-no-more">没有更多评价了</view>
        </view>
      </scroll-view>
    </view>
  </up-action-sheet>
</template>

<style lang="scss" scoped>
.f-bottom-box {
  box-shadow: rgba(0, 0, 0, 0.1) -10vw 0px 10vw;
}

.f-list-hidden {
  height: 13vw;
  background: linear-gradient(rgba(255, 255, 255, 0) 0%, #fff 70%);
}

.f-tag {
  @apply rounded px-4 py-1.5;
}

.f-img-preview {
  @apply w-screen h-screen;
}

.f-border-bottom {
  @apply border-b border-gray-200;
}

.o-color-aid {
  @apply text-gray-500;
}

/* 评论模块公共样式 */
.comment-list {
  @apply space-y-0;
}

.comment-item {
  @apply mb-4 pb-4;
}

.comment-header {
  @apply flex items-center justify-between mb-2;
}

.comment-avatar {
  @apply shrink-0;
}

.comment-user-info {
  @apply ml-2 flex-1 overflow-hidden;
}

.comment-username {
  @apply font-bold text-sm;
}

.comment-time {
  @apply flex text-xs text-gray-500;
}

.comment-rating {
  @apply shrink-0;
}

.comment-content {
  @apply my-2;
}

.comment-images {
  @apply flex flex-wrap gap-2 mt-2;
}

.comment-image {
  @apply relative overflow-hidden rounded;
}

.comment-loading {
  @apply py-4 text-center text-sm text-gray-500;
}

.comment-no-more {
  @apply py-4 text-center text-sm text-gray-500;
}

.comment-view-all {
  @apply text-sm text-gray-500 flex items-center gap-1 cursor-pointer hover:text-gray-700 transition-colors;
}
</style>
